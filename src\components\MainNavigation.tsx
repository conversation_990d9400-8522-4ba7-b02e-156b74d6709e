import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import { ChevronDown, ChevronUp, Settings, CreditCard, Users, Calculator, LayoutGrid, Table, Menu, X, LogOut, BarChart, Download, FileText, Calendar, Palette, Users2 } from 'lucide-react';

const MainNavigation: React.FC = () => {
  const location = useLocation();
  const { logout } = useAuth();
  const [isEMIDropdownOpen, setIsEMIDropdownOpen] = useState(false);
  const [isLeadsDropdownOpen, setIsLeadsDropdownOpen] = useState(false);
  const [isQuotesDropdownOpen, setIsQuotesDropdownOpen] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);

  const isActive = (path: string) => location.pathname === path;
  const isActiveSection = (paths: string[]) => paths.some(path => location.pathname.startsWith(path));

  const NavLink = ({ to, children, icon: Icon }: { to: string; children: React.ReactNode, icon: React.ElementType }) => (
    <Link
      to={to}
      className={`flex items-center gap-2 md:px-4 py-2 rounded-lg font-medium transition-colors duration-200 ${
        isActive(to)
          ? 'bg-primary-light text-primary'
          : 'text-gray-600 hover:text-primary hover:bg-primary-light/50'
      }`}
      onClick={() => setIsMobileMenuOpen(false)}
    >
      <Icon className="w-4 h-4 nav-icon" />
      {children}
    </Link>
  );

  const DropdownNavItem = ({
    label,
    isOpen,
    onToggle,
    children,
    isActiveSection: isActiveDropdown,
    icon: Icon
  }: {
    label: string;
    isOpen: boolean;
    onToggle: () => void;
    children: React.ReactNode;
    isActiveSection: boolean;
    icon: React.ElementType;
  }) => (
    <div className="relative">
      <button
        onClick={onToggle}
        className={`w-full text-left md:w-auto md:inline-flex px-4 py-2 rounded-lg font-medium transition-colors duration-200 flex items-center gap-2 ${
          isActiveDropdown
            ? 'bg-primary-light text-primary'
            : 'text-gray-600 hover:text-primary hover:bg-primary-light/50'
        }`}
      >
        <Icon className="w-4 h-4 nav-icon" />
        {label}
        {isOpen ? <ChevronUp className="w-4 h-4 ml-auto" /> : <ChevronDown className="w-4 h-4 ml-auto" />}
      </button>
      
      {isOpen && (
        <div className="md:absolute top-full left-0 mt-1 bg-white md:border md:border-gray-200 rounded-lg md:shadow-lg z-50 min-w-[200px]">
          <div className="pl-4 md:pl-0">
            {children}
          </div>
        </div>
      )}
    </div>
  );

  const DropdownLink = ({ to, icon: Icon, children }: { 
    to: string; 
    icon?: React.ElementType;
    children: React.ReactNode 
  }) => (
    <Link
      to={to}
      className={`flex items-center gap-2 px-4 py-2 text-sm transition-colors duration-200 hover:bg-gray-50 ${
        isActive(to) ? 'bg-primary-light text-primary' : 'text-gray-600 hover:text-primary'
      }`}
      onClick={() => {
        closeAllDropdowns();
        setIsMobileMenuOpen(false);
      }}
    >
      {Icon && <Icon className="w-4 h-4 nav-icon" />}
      {children}
    </Link>
  );

  useEffect(() => {
    if (isMobileMenuOpen) {
      document.body.style.overflow = 'hidden';
    } else {
      document.body.style.overflow = 'auto';
    }
    return () => {
      document.body.style.overflow = 'auto';
    };
  }, [isMobileMenuOpen]);

  useEffect(() => {
    // Close dropdowns when route changes
    closeAllDropdowns();
  }, [location.pathname]);


  const closeAllDropdowns = () => {
    setIsEMIDropdownOpen(false);
    setIsLeadsDropdownOpen(false);
    setIsQuotesDropdownOpen(false);
  };

  const NavLinks = () => (
    <>
      <NavLink to="/dashboard" icon={BarChart}>Dashboard</NavLink>
      <DropdownNavItem
        label="Leads"
        icon={Download}
        isOpen={isLeadsDropdownOpen}
        onToggle={() => {
          setIsLeadsDropdownOpen(!isLeadsDropdownOpen);
          setIsEMIDropdownOpen(false);
        }}
        isActiveSection={isActiveSection(['/leads'])}
      >
        <DropdownLink to="/leads" icon={LayoutGrid}>Kanban View</DropdownLink>
        <DropdownLink to="/leads/table" icon={Table}>Table View</DropdownLink>
        <div className="border-t border-gray-200 my-1"></div>
        <DropdownLink to="/leads/new" icon={Users}>New Lead</DropdownLink>
      </DropdownNavItem>
      <DropdownNavItem
        label="Quotes"
        icon={FileText}
        isOpen={isQuotesDropdownOpen}
        onToggle={() => {
          setIsQuotesDropdownOpen(!isQuotesDropdownOpen);
          setIsLeadsDropdownOpen(false);
          setIsEMIDropdownOpen(false);
        }}
        isActiveSection={isActiveSection(['/quotes', '/saved-quotes', '/destination-quotes'])}
      >
        <DropdownLink to="/saved-quotes" icon={Table}>Saved Quotes</DropdownLink>
        <DropdownLink to="/itinerary-management" icon={Calendar}>Itinerary Management</DropdownLink>
        <div className="border-t border-gray-200 my-1"></div>
        <DropdownLink to="/quotes" icon={Calculator}>New Quote</DropdownLink>
      </DropdownNavItem>
      <NavLink to="/followups" icon={Calendar}>Follow-ups</NavLink>
      <NavLink to="/promo-generator" icon={Palette}>Promo Generator</NavLink>
      <NavLink to="/customers" icon={Users2}>Customers</NavLink>
      <DropdownNavItem
        label="EMI Management"
        icon={Calculator}
        isOpen={isEMIDropdownOpen}
        onToggle={() => {
          setIsEMIDropdownOpen(!isEMIDropdownOpen);
          setIsLeadsDropdownOpen(false);
        }}
        isActiveSection={isActiveSection(['/emi', '/family-types'])}
      >
        <DropdownLink to="/emi/dashboard" icon={Settings}>EMI Dashboard</DropdownLink>
        <DropdownLink to="/emi/transactions" icon={CreditCard}>Payment Transactions</DropdownLink>
        <DropdownLink to="/family-types" icon={Users}>Family Type Management</DropdownLink>
        <DropdownLink to="/emi/calculator" icon={Calculator}>Prepaid EMI Calculator</DropdownLink>
      </DropdownNavItem>
    </>
  );

  return (
    <>
      <nav className="bg-white shadow-md border-b border-gray-200 sticky top-0 z-40">
        <div className="container mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex justify-between items-center h-16">
            <div className="flex items-center">
              <Link to="/dashboard" className="text-primary font-bold text-xl">
                TripXplo CRM
              </Link>
            </div>

            <div className="hidden md:flex items-center space-x-1">
              <NavLinks />
            </div>

            <div className="hidden md:flex items-center space-x-4">
              <button 
                onClick={() => logout()}
                className="px-3 py-1 text-sm bg-gray-200 hover:bg-gray-300 text-gray-700 rounded"
              >
                Logout
              </button>
            </div>

            <div className="md:hidden flex items-center">
              <button
                onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
                className="inline-flex items-center justify-center p-2 rounded-md text-gray-600 hover:text-primary hover:bg-gray-100 focus:outline-none focus:ring-2 focus:ring-inset focus:ring-primary"
              >
                <Menu className="h-6 w-6" />
              </button>
            </div>
          </div>
        </div>
      </nav>

      {/* Mobile Menu */}
      <div
        className={`fixed inset-0 z-50 md:hidden transition-transform transform ease-in-out duration-300 ${
          isMobileMenuOpen ? 'translate-x-0' : '-translate-x-full'
        }`}
      >
        {/* Overlay */}
        <div
          className={`fixed inset-0 bg-black transition-opacity ease-in-out duration-300 ${
            isMobileMenuOpen ? 'opacity-50' : 'opacity-0 pointer-events-none'
          }`}
          onClick={() => setIsMobileMenuOpen(false)}
        ></div>

        {/* Drawer */}
        <div className="relative z-10 w-80 max-w-[calc(100%-4rem)] h-full bg-white shadow-2xl flex flex-col">
          <div className="flex justify-between items-center p-4 border-b border-gray-200">
            <h2 className="font-bold text-lg text-primary">TripXplo CRM</h2>
            <button 
              onClick={() => setIsMobileMenuOpen(false)}
              className="p-2 rounded-full hover:bg-gray-100"
            >
              <X className="h-6 w-6 text-gray-600" />
            </button>
          </div>
          <div className="flex-1 overflow-y-auto p-4 space-y-2">
            <NavLinks />
          </div>
          <div className="p-4 border-t border-gray-200">
            <button
              onClick={() => {
                logout();
                setIsMobileMenuOpen(false);
              }}
              className="w-full text-left flex items-center gap-2 px-4 py-3 rounded-lg text-base font-medium text-red-600 hover:bg-red-50"
            >
              <LogOut className="w-5 h-5" />
              <span>Logout</span>
            </button>
          </div>
        </div>
      </div>

      {(isEMIDropdownOpen || isLeadsDropdownOpen || isQuotesDropdownOpen) && (
        <div 
          className="fixed inset-0 z-30" 
          onClick={closeAllDropdowns}
        />
      )}
    </>
  );
};

export default MainNavigation;
