import { jsPDF } from 'jspdf';
import { autoTable as originalAutoTable } from 'jspdf-autotable';
import { formatPriceForPDF, getActiveCurrencyCode } from './formatters';

// Import logo
import logoImage from '../../assets/tripxplo-logo-crop-DAR3srj4.png';

// Import font
import { montserratRegularBase64 } from '../assets/fonts/montserrat-regular.base64';

// Type definitions
interface HeaderWithRows {
  id?: string;
  title: string;
  order: number;
  rows: {
    id?: string;
    description: string;
    noUnit: number;
    price: number;
    igstPercentage: number;
    calculatedAmount: number;
    rowOrder: number;
  }[];
}

interface InvoiceData {
  invoiceNumber: string;
  invoiceDate: string;
  dueDate: string;
  customerName: string;
  customerEmail: string;
  customerPhone: string;
  packageName: string;
  destination: string;
  tripDuration: string;
  noOfPersons: number;
  children: number;
  infants: number;
  hotelRows: any[];
  costs: any;
  grandTotal: number;
  gstAmount: number;
  subtotal: number;
  currency: string;
  packageId: string;
  travelDate: string;
  // New field for unified header-rows structure
  headersWithRows?: HeaderWithRows[];
  inclusions?: string[];
  exclusions?: string[];
  commissionAmount: number;
}

/**
 * Validates the invoice data to ensure all required fields are present and valid.
 * @param data - The invoice data to validate
 * @throws Error if validation fails
 */
const validateInvoiceData = (data: InvoiceData): void => {
  const requiredFields = [
    'invoiceNumber', 'invoiceDate', 'dueDate', 'customerName',
    'packageName', 'destination', 'tripDuration', 'grandTotal',
    'subtotal', 'currency', 'packageId', 'travelDate'
  ];

  for (const field of requiredFields) {
    if (!data[field as keyof InvoiceData]) {
      throw new Error(`Missing required field: ${field}`);
    }
  }

  if (data.grandTotal <= 0) {
    throw new Error('Grand total must be greater than 0');
  }

  if (data.noOfPersons <= 0) {
    throw new Error('Number of persons must be greater than 0');
  }
};

// Company information
const COMPANY_INFO = {
  address: ' #29/108, Old Bridge Road (Near Vishnu Cinemas),\n Viruthampet,',
  city: 'Vellore, Tamil Nadu 632006.',
  phone: '+91 94424 24492',
  email: '<EMAIL>',
  website: 'www.tripxplo.com',
  gst: '33AAICT9828J1ZT'
};

// Theme colors
const THEME = {
  primary: '#15ae8b',
  secondary: '#f8f9fa',
  text: '#333333',
  lightGray: '#e9ecef'
};

/**
 * Formats a date string to "02nd Sep 2026" format.
 * @param dateStr - The date string to format
 * @returns The formatted date string
 */
const formatDateForInvoice = (dateStr: string): string => {
  const date = new Date(dateStr);
  const day = date.getDate();
  const month = date.toLocaleString('en-US', { month: 'short' });
  const year = date.getFullYear();
  const ordinal = getOrdinal(day);
  return `${day}${ordinal} ${month} ${year}`;
};

/**
 * Gets the ordinal suffix for a number (1st, 2nd, 3rd, etc.).
 * @param n - The number
 * @returns The ordinal suffix
 */
const getOrdinal = (n: number): string => {
  if (n > 3 && n < 21) return 'th';
  switch (n % 10) {
    case 1: return 'st';
    case 2: return 'nd';
    case 3: return 'rd';
    default: return 'th';
  }
};

/**
 * Converts a string to title case.
 * @param str - The string to convert
 * @returns The title case string
 */
const toTitleCase = (str: string): string => {
  return str.toLowerCase().replace(/\b\w/g, (char) => char.toUpperCase());
};

/**
 * Generates a PDF invoice document with the provided data.
 * @param invoiceData - The data to populate the invoice
 * @returns The generated jsPDF document
 * @throws Error if validation fails or generation encounters an error
 */
export const generateInvoice = (invoiceData: InvoiceData): jsPDF => {
  try {
    // Validate input data
    validateInvoiceData(invoiceData);

    const doc = new jsPDF({ orientation: 'p', unit: 'mm', format: 'a4', compress: true });
    const pageWidth = doc.internal.pageSize.getWidth();
    const pageHeight = doc.internal.pageSize.getHeight();

    // Add custom font for rupee symbol
    doc.addFileToVFS('Montserrat-Regular.ttf', montserratRegularBase64);
    doc.addFont('Montserrat-Regular.ttf', 'Montserrat', 'normal');
    doc.setFont('Montserrat', 'normal');

    // Add logo
    try {
      doc.addImage(logoImage, 'PNG', 15, 15, 40, 20);
    } catch (error) {
      console.warn('Logo not found, continuing without logo');
    }


  // Company tagline
  doc.setFontSize(9);
  doc.setTextColor('#666666');
  doc.setFont('helvetica', 'italic');
  doc.text('Your Travel Partner', 20, 34);

  // Company Info
  doc.setFontSize(9);
  doc.setTextColor(THEME.text);

  doc.setFont('helvetica', 'bold');
  doc.text('Address:', 20, 39);
  doc.setFont('helvetica', 'normal');
  const addressLines = doc.splitTextToSize(COMPANY_INFO.address, 100);
  doc.text(addressLines, 20 + doc.getTextWidth('Address: ') + 2, 39);

  doc.setFont('helvetica', 'bold');
  doc.text('City:', 20, 47);
  doc.setFont('helvetica', 'normal');
  doc.text(COMPANY_INFO.city, 20 + doc.getTextWidth('City: ') + 2, 47);

  doc.setFont('helvetica', 'bold');
  doc.text('Phone:', 20, 52);
  doc.setFont('helvetica', 'normal');
  doc.text(COMPANY_INFO.phone, 20 + doc.getTextWidth('Phone: ') + 2, 52);

  doc.setFont('helvetica', 'bold');
  doc.text('Email:', 20, 56);
  doc.setFont('helvetica', 'normal');
  doc.text(COMPANY_INFO.email, 20 + doc.getTextWidth('Email: ') + 2, 56);

  doc.setFont('helvetica', 'bold');
  doc.text('Website:', 20, 60);
  doc.setFont('helvetica', 'normal');
  doc.text(COMPANY_INFO.website, 20 + doc.getTextWidth('Website: ') + 2, 60);

  doc.setFont('helvetica', 'bold');
  doc.text('GST Number:', 20, 64);
  doc.setFont('helvetica', 'normal');
  doc.text(COMPANY_INFO.gst, 20 + doc.getTextWidth('GST Number: ') + 2, 64);

  // Invoice Title with background
  doc.setFillColor(THEME.primary);
  doc.rect(pageWidth - 70, 20, 55, 15, 'F');
  doc.setFontSize(18);
  doc.setTextColor(255, 255, 255);
  doc.setFont('helvetica', 'bold');
  doc.text('INVOICE', pageWidth - 42.5, 30, { align: 'center' });

  // Invoice Details
  doc.setFontSize(10);
  doc.setTextColor(THEME.text);
  doc.setFont('helvetica', 'normal');
  doc.text(`Invoice #: ${invoiceData.invoiceNumber}`, pageWidth - 15, 45, { align: 'right' });
  doc.text(`Date: ${formatDateForInvoice(invoiceData.invoiceDate)}`, pageWidth - 15, 52, { align: 'right' });
  doc.text(`Due Date: ${formatDateForInvoice(invoiceData.dueDate)}`, pageWidth - 15, 59, { align: 'right' });

  // Customer Information with background
  let yPos = 70;
  doc.setFillColor(248, 249, 250);
  doc.setDrawColor(224, 224, 224);
  doc.setLineWidth(0.1);
  doc.rect(15, yPos, 85, 45, 'FD');

  doc.setFontSize(12);
  doc.setTextColor(THEME.primary);
  doc.setFont('helvetica', 'bold');
  doc.text('Bill To:', 20, yPos + 8);

  doc.setFontSize(11);
  doc.setTextColor(THEME.text);
  doc.setFont('helvetica', 'normal');
  
  const customerNameLines = doc.splitTextToSize(invoiceData.customerName, 80);
  doc.text(customerNameLines, 20, yPos + 18);
  
  let yPosAfterName = yPos + 18 + (customerNameLines.length - 1) * 5;

  if (invoiceData.customerEmail) {
    const customerEmailLines = doc.splitTextToSize(invoiceData.customerEmail, 80);
    doc.text(customerEmailLines, 20, yPosAfterName + 8);
    yPosAfterName += (customerEmailLines.length - 1) * 5 + 8;
  }
  
  if (invoiceData.customerPhone) {
    doc.text(invoiceData.customerPhone, 20, yPosAfterName + 8);
  }

  // Package Information with background
  doc.setFillColor(248, 249, 250);
  doc.rect(pageWidth - 100, yPos, 85, 45, 'FD');

  doc.setFontSize(12);
  doc.setTextColor(THEME.primary);
  doc.setFont('helvetica', 'bold');
  doc.text('Package Details:', pageWidth - 95, yPos + 8);

  doc.setFontSize(10);
  doc.setTextColor(THEME.text);
  doc.setFont('helvetica', 'normal');
  
  doc.setFont('helvetica', 'bold');
  doc.text('Package:', pageWidth - 95, yPos + 18);
  doc.setFont('helvetica', 'normal');
  const packageNameLines = doc.splitTextToSize(invoiceData.packageName, 60);
  const packageLabelWidth = doc.getTextWidth('Package: ') + 2;
  doc.text(packageNameLines, pageWidth - 95 + packageLabelWidth, yPos + 18);

  let yPosAfterPackage = yPos + 18 + (packageNameLines.length - 1) * 5;

  doc.setFont('helvetica', 'bold');
  doc.text('Destination:', pageWidth - 95, yPosAfterPackage + 4);
  doc.setFont('helvetica', 'normal');
  doc.text(invoiceData.destination, pageWidth - 95 + doc.getTextWidth('Destination: ') + 2, yPosAfterPackage + 4);
  doc.setFont('helvetica', 'bold');
  doc.text('Duration:', pageWidth - 95, yPosAfterPackage + 9);
  doc.setFont('helvetica', 'normal');
  doc.text(invoiceData.tripDuration, pageWidth - 95 + doc.getTextWidth('Duration: ') + 2, yPosAfterPackage + 9);
  doc.setFont('helvetica', 'bold');
  doc.text('Travel Date:', pageWidth - 95, yPosAfterPackage + 14);
  doc.setFont('helvetica', 'normal');
  doc.text(formatDateForInvoice(invoiceData.travelDate), pageWidth - 95 + doc.getTextWidth('Travel Date: ') + 2, yPosAfterPackage + 14);


  // Add page border for professionalism
  doc.setDrawColor(THEME.primary);
  doc.setLineWidth(0.8);
  doc.rect(10, 10, pageWidth - 20, pageHeight - 20);

  return doc;
  } catch (error) {
    console.error('Error generating invoice:', error);
    throw new Error(`Failed to generate invoice: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Generates and opens a preview of the invoice PDF in a new browser tab.
 * @param invoiceData - The data to populate the invoice
 * @throws Error if PDF generation or preview fails
 */
export const generateInvoicePreview = (invoiceData: InvoiceData): void => {
  try {
    const doc = generateInvoice(invoiceData);
    const result = addInvoiceTable(doc, invoiceData);
    invoiceData.subtotal = result.subtotal;
    invoiceData.gstAmount = result.gstTotal;
    invoiceData.grandTotal = result.totalAmount;
    addInvoiceTotals(doc, invoiceData, result.tableEndY);
    addInvoiceFooter(doc, invoiceData);

    // Open preview
    const pdfBlob = doc.output('blob');
    const pdfUrl = URL.createObjectURL(pdfBlob);
    window.open(pdfUrl, '_blank');
  } catch (error) {
    console.error('Error generating invoice preview:', error);
    throw new Error(`Failed to generate invoice preview: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Generates and returns the URL of the invoice PDF preview.
 * @param invoiceData - The data to populate the invoice
 * @returns The URL of the generated PDF
 * @throws Error if PDF generation fails
 */
export const generateInvoicePreviewUrl = (invoiceData: InvoiceData): string => {
  try {
    const doc = generateInvoice(invoiceData);
    const result = addInvoiceTable(doc, invoiceData);
    invoiceData.subtotal = result.subtotal;
    invoiceData.gstAmount = result.gstTotal;
    invoiceData.grandTotal = result.totalAmount;
    addInvoiceTotals(doc, invoiceData, result.tableEndY);
    addInvoiceFooter(doc, invoiceData);

    const pdfBlob = doc.output('blob');
    const pdfUrl = URL.createObjectURL(pdfBlob);
    return pdfUrl;
  } catch (error) {
    console.error('Error generating invoice preview URL:', error);
    throw new Error(`Failed to generate invoice preview: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};

/**
 * Adds the invoice table with accommodation and cost details to the PDF document.
 * @param doc - The jsPDF document
 * @param invoiceData - The invoice data
 * @returns The Y position after the table
 */
const addInvoiceTable = (doc: jsPDF, invoiceData: InvoiceData): { tableEndY: number; subtotal: number; gstTotal: number; totalAmount: number } => {
  const tableData: string[][] = [];
  let subtotal = 0;
  let gstTotal = 0;
  let totalAmount = 0;

  // Add accommodation details
  if (invoiceData.hotelRows && invoiceData.hotelRows.length > 0) {
    // Add accommodation header
    tableData.push([
      'Accommodation',
      '',
      '',
      '',
      ''
    ]);

    // Calculate total hotel price including costs
    let totalHotelPrice = 0;
    let totalUnits = invoiceData.noOfPersons; // Use no_of_persons from quotes table
    let hotelNames: string[] = [];

    invoiceData.hotelRows.forEach((hotel) => {
      if (hotel.hotelName && hotel.stayPrice > 0) {
        totalHotelPrice += hotel.stayPrice;
        hotelNames.push(hotel.hotelName);
      }
    });

    // Add costs if it's a number
    if (typeof invoiceData.costs === 'number') {
      totalHotelPrice += invoiceData.costs;
    }

    // Set price column to subtotal, amount to subtotal * units
    const gstPercentage = 5;
    let priceBeforeGST = (invoiceData.commissionAmount + invoiceData.subtotal) / 2;
    totalHotelPrice = priceBeforeGST * totalUnits;
    let gstAmount = totalHotelPrice * (gstPercentage / 100);

    // Add the accommodation row
    tableData.push([
      `${hotelNames.map(name => `  ${name}`).join('\n')}`,
      totalUnits.toString(),
      formatPriceForPDF(priceBeforeGST, 'INR', getActiveCurrencyCode()),
      formatPriceForPDF(gstAmount, 'INR', getActiveCurrencyCode()),
      formatPriceForPDF(totalHotelPrice, 'INR', getActiveCurrencyCode())
    ]);

    // Update sums for accommodation
    subtotal += totalHotelPrice ;
    gstTotal += gstAmount;
    totalAmount += totalHotelPrice + gstAmount;
  }

  // Add headers with their rows using new unified structure
  if (invoiceData.headersWithRows && invoiceData.headersWithRows.length > 0) {
    invoiceData.headersWithRows.forEach((header) => {
      // Add header as a section divider
      tableData.push([
        `${toTitleCase(header.title)}`,
        '',
        '',
        '',
        ''
      ]);

      // Add rows for this header
      header.rows.forEach((row) => {
        if (row.description && (row.noUnit > 0 || row.price > 0)) {
          const rowSubtotal = row.price * row.noUnit;
          const rowGst = rowSubtotal * (row.igstPercentage / 100);
          const rowTotal = row.calculatedAmount;

          subtotal += rowSubtotal;
          gstTotal += rowGst;
          totalAmount += rowTotal;

          tableData.push([
            `${row.description.split('\n').map(line => `  ${line}`).join('\n')}`, // Indent to show it belongs to the header
            row.noUnit.toString(),
            formatPriceForPDF(row.price, 'INR', getActiveCurrencyCode()),
            formatPriceForPDF(rowGst, 'INR', getActiveCurrencyCode()),
            formatPriceForPDF(row.calculatedAmount, 'INR', getActiveCurrencyCode())
          ]);
        }
      });
    });
  }

  // Remove other costs section completely as per requirements

  // Add table with enhanced styling - Updated headers to match new structure
  (originalAutoTable as any)(doc, {
    startY: 120,
    head: [['Description', 'No Unit', 'Price', 'GST', 'Amount']],
    body: tableData,
    theme: 'grid',
    headStyles: {
      fillColor: THEME.primary,
      textColor: 255,
      fontSize: 12,
      fontStyle: 'bold',
      font: 'Montserrat',
      halign: 'center',
      valign: 'middle',
      cellPadding: 2
    },
    bodyStyles: {
      fontSize: 10,
      textColor: THEME.text,
      font: 'Montserrat',
      cellPadding: 3,
      lineColor: '#e0e0e0',
      lineWidth: 0.1
    },
    alternateRowStyles: {
      fillColor: '#f8f9fa'
    },
    columnStyles: {
      0: { cellWidth: 85, halign: 'left' },   // Description
      1: { cellWidth: 16, halign: 'center' }, // No. Unit
      2: { cellWidth: 28, halign: 'right' },  // Price
      3: { cellWidth: 26, halign: 'center' }, // IGST
      4: { cellWidth: 25, halign: 'right' }   // Amount
    },
    margin: { left: 15, right: 15 },
    tableLineColor: '#e0e0e0',
    tableLineWidth: 0.1,
    didParseCell: function(data: any) {
      // Make section headers bold (non-indented descriptions in column 0)
      if (data.column.index === 0 && data.cell.raw && !data.cell.raw.startsWith('  ')) {
        data.cell.styles.fontStyle = 'bold';
        data.cell.styles.fontSize = 10;
      }
      // Reduce font size for indented rows (hotel names and descriptions)
      if (data.column.index === 0 && data.cell.raw && data.cell.raw.startsWith('  ')) {
        data.cell.styles.fontSize = 8;
      }
      // Reduce font size for columns 1, 2, 3, 4 (No Unit, Price, GST, Amount)
      if (data.column.index > 0 && data.column.index <= 4) {
        data.cell.styles.fontSize = 9;
      }
    }
  });

  const tableEndY = (doc as any).lastAutoTable.finalY;
  return { tableEndY, subtotal, gstTotal, totalAmount: Math.round(totalAmount) };
};

/**
 * Adds the subtotal, GST, and total amounts to the PDF document.
 * @param doc - The jsPDF document
 * @param invoiceData - The invoice data
 * @param startY - The Y position to start adding totals
 */
const addInvoiceTotals = (doc: jsPDF, invoiceData: InvoiceData, startY: number): void => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const finalY = startY + 15;

  // Create a background box for totals
  doc.setFillColor(248, 249, 250); // Light gray background
  doc.setDrawColor(224, 224, 224); // Light border
  doc.setLineWidth(0.1);
  doc.rect(pageWidth - 80, finalY - 5, 65, 35, 'FD'); // Fill and Draw

  doc.setFontSize(10);
  doc.setTextColor(THEME.text);

  // Subtotal
  doc.text('Subtotal:', pageWidth - 75, finalY + 3);
  doc.setFont('Montserrat');
  doc.text(formatPriceForPDF(invoiceData.subtotal, 'INR', getActiveCurrencyCode()), pageWidth - 20, finalY + 3, { align: 'right' });

  // GST
  if (invoiceData.gstAmount > 0) {
    doc.text('GST:', pageWidth - 75, finalY + 10);
    doc.setFont('Montserrat');
    doc.text(formatPriceForPDF(invoiceData.gstAmount, 'INR', getActiveCurrencyCode()), pageWidth - 20, finalY + 10, { align: 'right' });
  }

  // Separator line
  doc.setDrawColor(THEME.primary);
  doc.setLineWidth(0.3);
  doc.line(pageWidth - 75, finalY + 14, pageWidth - 20, finalY + 14);

  // Total
  doc.setFontSize(12);
  doc.setFont('Montserrat', 'bold');
  doc.setTextColor(THEME.primary);
  doc.text('Total Amount:', pageWidth - 75, finalY + 21);

  // Format the total amount and split into symbol and number for styling
  const formattedTotal = formatPriceForPDF(invoiceData.grandTotal, 'INR', getActiveCurrencyCode());
  const [symbol, numberPart] = formattedTotal.split(' ');

  // Calculate positions for right alignment
  const totalWidth = doc.getTextWidth(formattedTotal);
  const symbolWidth = doc.getTextWidth(symbol + ' ');

  // Position from the right edge
  const startX = pageWidth - 20 - totalWidth;

  // Draw symbol in normal font
  doc.setFont('Montserrat', 'normal');
  doc.text(symbol + ' ', startX, finalY + 21);

  // Draw number in bold font
  doc.setFont('Montserrat', 'bold');
  doc.text(numberPart, startX + symbolWidth, finalY + 21);

  // Reset font
  doc.setFont('Montserrat', 'normal');
};

/**
 * Adds the footer with bank details, terms & conditions, and thank you message to the PDF document.
 * @param doc - The jsPDF document
 * @param invoiceData - The invoice data
 */
const addInvoiceFooter = (doc: jsPDF, invoiceData: InvoiceData): void => {
  const pageWidth = doc.internal.pageSize.getWidth();
  const pageHeight = doc.internal.pageSize.getHeight();

  // Add a new page for footer content
  doc.addPage();
  let footerY = 20;

  // Add page border for professionalism
  doc.setDrawColor(THEME.primary);
  doc.setLineWidth(0.8);
  doc.rect(10, 10, pageWidth - 20, pageHeight - 20);

  // Bank Details
  doc.setFontSize(10);
  doc.setTextColor(THEME.text);
  doc.setFont('Montserrat', 'bold');
  doc.text('BANK DETAILS:', 15, footerY);
  doc.setFont('Montserrat', 'normal');
  doc.setFontSize(9);
  doc.text('BANK: ICICI BANK', 15, footerY + 6);
  doc.text('Account Number: ************', 15, footerY + 12);
  doc.text('Account Name: TRIPMILESTONE TOURS PRIVATE LIMITED', 15, footerY + 18);
  doc.text('IFSC Code: ICIC0001910', 15, footerY + 24);
  doc.text('Branch: VELLORE - GANDHI NAGAR TAMIL NADU', 15, footerY + 30);

  footerY += 40;

  // Terms & Conditions
  doc.setFont('Montserrat', 'bold');
  doc.setFontSize(10);
  doc.text('Terms & Conditions', 15, footerY);
  doc.setFont('Montserrat', 'normal');
  doc.setFontSize(9);
  doc.text('Confirmation Policy', 15, footerY + 6);
  doc.text('The customer receives a confirmation voucher via email within 24 hours of successful booking.', 15, footerY + 12);

  footerY += 20;

  // Inclusions
  if (invoiceData.inclusions && invoiceData.inclusions.length > 0) {
    doc.setFont('Montserrat', 'bold');
    doc.text('Inclusions:', 15, footerY);
    doc.setFont('Montserrat', 'normal');
    const inclusionsText = invoiceData.inclusions.map(item => `- ${item}`).join('\n');
    const inclusionsLines = doc.splitTextToSize(inclusionsText, pageWidth - 30);
    doc.text(inclusionsLines, 15, footerY + 6);
    footerY += 6 + (inclusionsLines.length * 4);
  }

  // Exclusions
  if (invoiceData.exclusions && invoiceData.exclusions.length > 0) {
    doc.setFont('Montserrat', 'bold');
    doc.text('Exclusions:', 15, footerY);
    doc.setFont('Montserrat', 'normal');
    const exclusionsText = invoiceData.exclusions.map(item => `- ${item}`).join('\n');
    const exclusionsLines = doc.splitTextToSize(exclusionsText, pageWidth - 30);
    doc.text(exclusionsLines, 15, footerY + 6);
    footerY += 6 + (exclusionsLines.length * 4);
  }

  // Note
  doc.setFont('Montserrat', 'normal');
  doc.setFontSize(8);
  doc.text('*NOTE: Any up gradation in Train or Hotel category, price may change.', 15, footerY);
  footerY += 8;

  // Cancellation Policy
  doc.setFont('Montserrat', 'bold');
  doc.setFontSize(9);
  doc.text('Cancellation Policy', 15, footerY);
  doc.setFont('Montserrat', 'normal');
  doc.setFontSize(8);
  doc.text('95% Refund on Cancellation up to 30 days before the Departure date (In Case of Booking is done,\nthe booking amount is non-refundable)', 15, footerY + 6);
  doc.text('Date of booking to 30 - 15 days the cancellation charges will be 25% of the tour cost.', 15, footerY + 13);
  doc.text('15 to less than 7 days -Total amount will be forfeited and no refund shall be given.', 15, footerY + 18);

  footerY += 30;

  // Thank you message
  doc.setFontSize(10);
  doc.setTextColor(THEME.primary);
  doc.setFont('Montserrat', 'bold');
  doc.text('Thank you for choosing TripXplo!', pageWidth / 2, footerY, { align: 'center' });
};

/**
 * Generates and downloads the invoice PDF file.
 * @param invoiceData - The data to populate the invoice
 * @throws Error if PDF generation or download fails
 */
export const downloadInvoice = (invoiceData: InvoiceData): void => {
  try {
    const doc = generateInvoice(invoiceData);
    const result = addInvoiceTable(doc, invoiceData);
    invoiceData.subtotal = result.subtotal;
    invoiceData.gstAmount = result.gstTotal;
    invoiceData.grandTotal = result.totalAmount;
    addInvoiceTotals(doc, invoiceData, result.tableEndY);
    addInvoiceFooter(doc, invoiceData);

    // Generate filename
    const filename = `Invoice_${invoiceData.invoiceNumber}_${invoiceData.customerName.replace(/\s+/g, '_')}_${new Date().toISOString().split('T')[0]}.pdf`;

    doc.save(filename);
  } catch (error) {
    console.error('Error downloading invoice:', error);
    throw new Error(`Failed to download invoice: ${error instanceof Error ? error.message : 'Unknown error'}`);
  }
};
